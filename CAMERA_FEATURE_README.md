# 微信式相机功能实现

## 功能概述

本次更新实现了类似微信的相机功能，将拍照和录视频合并为一个统一的相机界面。

## 主要特性

### 1. 统一的相机界面
- **轻触拍照**：快速点击拍摄按钮即可拍照
- **长按录视频**：长按拍摄按钮开始录制视频，松开停止录制
- **实时预览**：全屏相机预览界面

### 2. 相机控制功能
- **前后摄像头切换**：支持前置和后置摄像头切换
- **闪光灯控制**：支持开启/关闭闪光灯（仅后置摄像头）
- **录制时间显示**：录制视频时显示实时录制时间
- **最大录制时长**：限制视频录制最长5分钟

### 3. 用户体验优化
- **震动反馈**：开始录制时提供触觉反馈
- **动画效果**：录制时拍摄按钮有呼吸动画效果
- **错误处理**：完善的错误提示和异常处理

## 技术实现

### 依赖库
- `camera: ^0.10.5+9` - Flutter 官方相机插件
- `image_picker: ^0.8.9` - 用于相册选择功能

### 核心文件
1. `lib/src/pages/find/moments/custom_camera_page.dart` - 自定义相机页面
2. `lib/src/pages/find/moments/moments_page.dart` - 朋友圈页面（已修改）

### 权限配置

#### Android (android/app/src/main/AndroidManifest.xml)
```xml
<uses-permission android:name="android.permission.CAMERA" />
<uses-permission android:name="android.permission.RECORD_AUDIO" />
```

#### iOS (ios/Runner/Info.plist)
```xml
<key>NSCameraUsageDescription</key>
<string>需要访问相机来拍摄照片和录制视频</string>
<key>NSMicrophoneUsageDescription</key>
<string>需要访问麦克风来录制视频</string>
```

## 使用方法

### 1. 在朋友圈页面
1. 点击右上角的 "+" 按钮
2. 选择 "拍摄" 选项（显示"轻触拍照，长按摄像"提示）

### 2. 在相机界面
1. **拍照**：轻触底部的白色圆形按钮
2. **录视频**：长按底部的白色圆形按钮，松开停止
3. **切换摄像头**：点击右上角的翻转图标
4. **开关闪光灯**：点击右上角的闪光灯图标（仅后置摄像头）
5. **访问相册**：点击底部的"相册"按钮
6. **关闭相机**：点击左上角的关闭按钮

### 3. 操作提示
- 界面底部显示"轻触拍照，长按摄像"提示文字
- 录制视频时，按钮会变红色并显示录制时间
- 录制过程中按钮有呼吸动画效果

## 界面布局

```
┌─────────────────────────────────┐
│ [X]              [⚡] [🔄]      │  <- 顶部控制栏
│                                 │
│                                 │
│        相机预览区域              │
│                                 │
│                                 │
│                                 │
│     轻触拍照，长按摄像           │  <- 提示文字
│                                 │
│           [●]                   │  <- 拍摄按钮
│                                 │
│          [相册]                 │  <- 相册按钮
└─────────────────────────────────┘
```

## 注意事项

1. **权限要求**：首次使用需要用户授权相机和麦克风权限
2. **存储空间**：录制视频需要足够的存储空间
3. **性能考虑**：长时间录制可能影响设备性能
4. **兼容性**：支持 iOS 和 Android 平台

## 后续优化建议

1. 添加美颜滤镜功能
2. 支持变焦功能
3. 添加更多录制设置选项
4. 优化低光环境下的拍摄效果
5. 添加拍摄声音开关选项
