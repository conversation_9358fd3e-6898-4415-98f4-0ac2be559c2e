name: tencent_cloud_chat_demo
description: The sample app of Tencent Cloud Chat Flutter integration. Includes two different interface for variety width that runs on iOS/Android/macOS/Windows/Web/H5 platform. This app is mainly build with our Flutter Chat TUIKit, which can adaptively display and business logic according to the type of platform and screen width.

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 1.0.0+2

# If you running this sample app on Flutter 3.7 or higher, please modify this configurations. And add the following code to `pubspec.yaml`:
#dependency_overrides:
#  modal_bottom_sheet: ^3.0.0-pre
#  tencent_extended_text_field: ^1.0.1
#  tencent_extended_text: ^1.0.2
environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.19.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:

  # 第三方登陆
  firebase_auth: ^4.12.0
  # Google登录
  google_sign_in: ^6.1.5
  # 判断是否支持google服务
  google_api_availability: ^5.0.0
  firebase_core: ^2.20.0
  flutter:
    sdk: flutter
  web: ^0.5.1
  flutter_localizations:
    sdk: flutter

  # Tencent Cloud Chat dependencies:
  tencent_cloud_chat_uikit: 
    path: ../chat-uikit-flutter
  tencent_calls_uikit: ^3.2.0
  tencent_cloud_chat_push: ^8.6.7019+1
  tencent_chat_i18n_tool: ^2.3.8
  tencent_cloud_chat_sdk: ^8.5.6864+6

  # Third party dependencies:
  cupertino_icons: ^1.0.2
  webview_flutter: ^3.0.0
  flutter_easyloading: ^3.0.3
  desktop_webview_window_for_is: ^0.2.4
  bitsdojo_window: ^0.1.5
  flutter_screenutil: ^5.9.0
  fluttertoast: ^8.2.1
  flutter_styled_toast: ^2.1.3
  dio: ^4.0.0
  shared_preferences: ^2.5.3
  flutter_slidable: ^3.0.0
  provider: ^6.0.0
  image_picker: ^0.8.9
  camera: ^0.10.5+9
  pull_to_refresh: ^2.0.0
  flutter_image_compress: ^2.3.0
  cached_network_image: ^3.3.0
  flutter_launcher_icons: ^0.13.1
  flutter_native_splash: ^2.3.10
  image_cropper: ^9.1.0
  sqflite: ^2.3.0
  path_provider: ^2.1.1
  flutter_facebook_auth: ^7.1.0
  mobile_scanner: ^6.0.10
  qr_flutter: ^4.1.0
  image_gallery_saver: ^2.0.3
dependency_overrides:
  tencent_chat_i18n_tool:
    path: ../tencent-chat-i18n-tool
dev_dependencies:
  build_runner: any
  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating localMsgspecific lint
  # rules and activating additional ones.
  flutter_lints: ^1.0.0
  msix: ^3.14.2

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  assets:
    - assets/
    - assets/custom_face_resource/4350/
    - assets/custom_face_resource/4351/
    - assets/custom_face_resource/4352/
    - assets/user_guide/small/zh/
    - assets/user_guide/small/en/
    - assets/user_guide/
    - assets/live/
    - lib/country_list_pick-1.0.1+5/flags/
    - assets/calling_message/
    - assets/user_info_card/
    - assets/login/
    - assets/empty/
    - assets/find/
    - assets/moments/
    - assets/red_envelope/
    - assets/task_page/
    - assets/serveice/
  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
flutter_intl:
  enabled: true

flutter_icons:
  android: true
  ios: true
  image_path: "assets/im_new_logo.jpg"

flutter_native_splash:
  background_image: "assets/splash_new.png"
  image: null
  android_gravity: center
  ios_content_mode: center
  fullscreen: true
  android: true
  ios: true
